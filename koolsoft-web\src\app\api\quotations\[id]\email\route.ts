import { NextRequest, NextResponse } from 'next/server';
import { withRoleProtection } from '@/lib/auth/middleware';
import { getQuotationRepository } from '@/lib/repositories';
import { quotationEmailSchema } from '@/lib/validations/quotation.schema';
import { getEmailService } from '@/lib/services/email.service';
import { z } from 'zod';
import PDFDocument from 'pdfkit';

/**
 * POST /api/quotations/[id]/email
 * Send quotation via email
 */
export const POST = withRoleProtection(
  ['ADMIN', 'MANAGER', 'EXECUTIVE'],
  async (
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { id } = await params;
      const body = await request.json();

      // Validate request body
      const validatedData = quotationEmailSchema.parse({ ...body, quotationId: id });

      const quotationRepository = getQuotationRepository();
      const quotation = await quotationRepository.findWithAllRelations(id);

      if (!quotation) {
        return NextResponse.json(
          {
            success: false,
            error: 'Quotation not found',
          },
          { status: 404 }
        );
      }

      let pdfAttachment = null;

      // Generate PDF attachment if requested
      if (validatedData.attachPdf) {
        try {
          const doc = new PDFDocument({ margin: 50 });
          const chunks: Buffer[] = [];

          doc.on('data', (chunk) => chunks.push(chunk));

          // Company Header
          doc.fontSize(20)
             .fillColor('#0F52BA')
             .text('KoolSoft Technologies', 50, 50);

          doc.fontSize(10)
             .fillColor('#666666')
             .text('Air Conditioning & Refrigeration Solutions', 50, 75)
             .text('Email: <EMAIL> | Phone: +91-XXXXXXXXXX', 50, 90);

          // Quotation Title
          doc.fontSize(16)
             .fillColor('#000000')
             .text('QUOTATION', 50, 120);

          // Quotation Details
          const quotationDetails = [
            ['Quotation Number:', quotation.quotationNumber],
            ['Date:', new Date(quotation.quotationDate).toLocaleDateString()],
            ['Valid Until:', quotation.validUntil ? new Date(quotation.validUntil).toLocaleDateString() : 'N/A'],
            ['Status:', quotation.status],
          ];

          let yPos = 140;
          doc.fontSize(10);
          quotationDetails.forEach(([label, value]) => {
            doc.text(label, 50, yPos)
               .text(value, 150, yPos);
            yPos += 15;
          });

          // Customer Details
          yPos += 20;
          doc.fontSize(12)
             .text('Bill To:', 50, yPos);

          yPos += 20;
          doc.fontSize(10)
             .text(quotation.customer.name, 50, yPos);

          if (quotation.customer.address) {
            yPos += 15;
            doc.text(quotation.customer.address, 50, yPos);
          }

          if (quotation.customer.city || quotation.customer.state || quotation.customer.pinCode) {
            yPos += 15;
            const location = [
              quotation.customer.city,
              quotation.customer.state,
              quotation.customer.pinCode
            ].filter(Boolean).join(', ');
            doc.text(location, 50, yPos);
          }

          if (quotation.customer.phone) {
            yPos += 15;
            doc.text(`Phone: ${quotation.customer.phone}`, 50, yPos);
          }

          if (quotation.customer.email) {
            yPos += 15;
            doc.text(`Email: ${quotation.customer.email}`, 50, yPos);
          }

          // Executive Details (right side)
          doc.fontSize(12)
             .text('Sales Executive:', 350, yPos - (quotation.customer.email ? 60 : 45));

          let execYPos = yPos - (quotation.customer.email ? 40 : 25);
          doc.fontSize(10)
             .text(quotation.executive.name, 350, execYPos);

          if (quotation.executive.designation) {
            execYPos += 15;
            doc.text(quotation.executive.designation, 350, execYPos);
          }

          if (quotation.executive.email) {
            execYPos += 15;
            doc.text(quotation.executive.email, 350, execYPos);
          }

          if (quotation.executive.phone) {
            execYPos += 15;
            doc.text(quotation.executive.phone, 350, execYPos);
          }

          // Subject
          if (quotation.subject) {
            yPos += 40;
            doc.fontSize(12)
               .text('Subject:', 50, yPos);
            doc.fontSize(10)
               .text(quotation.subject, 50, yPos + 15);
            yPos += 15;
          }

          // Items Table Header
          yPos += 40;
          const tableTop = yPos;
          const descriptionX = 50;
          const quantityX = 300;
          const priceX = 350;
          const totalX = 450;

          doc.fontSize(10)
             .text('Description', descriptionX, tableTop)
             .text('Qty', quantityX, tableTop)
             .text('Unit Price', priceX, tableTop)
             .text('Total', totalX, tableTop);

          // Draw line under header
          doc.moveTo(50, tableTop + 15)
             .lineTo(550, tableTop + 15)
             .stroke();

          // Items
          yPos = tableTop + 25;
          quotation.items.forEach((item) => {
            doc.text(item.description, descriptionX, yPos, { width: 240 })
               .text(item.quantity.toString(), quantityX, yPos)
               .text(`₹${item.unitPrice.toLocaleString()}`, priceX, yPos)
               .text(`₹${item.totalPrice.toLocaleString()}`, totalX, yPos);

            if (item.specifications) {
              yPos += 15;
              doc.fontSize(8)
                 .fillColor('#666666')
                 .text(item.specifications, descriptionX, yPos, { width: 240 });
              doc.fontSize(10)
                 .fillColor('#000000');
            }

            yPos += 25;
          });

          // Totals
          yPos += 20;
          const totalsX = 400;

          doc.text('Subtotal:', totalsX, yPos)
             .text(`₹${quotation.subtotal.toLocaleString()}`, totalsX + 80, yPos);

          if (quotation.discount && quotation.discount > 0) {
            yPos += 15;
            const discountText = quotation.discountType === 'PERCENTAGE' 
              ? `${quotation.discount}%` 
              : `₹${quotation.discount.toLocaleString()}`;
            doc.text('Discount:', totalsX, yPos)
               .text(discountText, totalsX + 80, yPos);
          }

          yPos += 15;
          doc.text('Tax Amount:', totalsX, yPos)
             .text(`₹${quotation.taxAmount.toLocaleString()}`, totalsX + 80, yPos);

          // Total with line
          yPos += 15;
          doc.moveTo(totalsX, yPos)
             .lineTo(totalsX + 120, yPos)
             .stroke();

          yPos += 10;
          doc.fontSize(12)
             .text('Total Amount:', totalsX, yPos)
             .text(`₹${quotation.totalAmount.toLocaleString()}`, totalsX + 80, yPos);

          // Notes
          if (quotation.notes) {
            yPos += 40;
            doc.fontSize(10)
               .text('Notes:', 50, yPos);
            doc.text(quotation.notes, 50, yPos + 15, { width: 500 });
          }

          // Terms and Conditions
          if (quotation.termsConditions) {
            yPos += (quotation.notes ? 60 : 40);
            doc.fontSize(10)
               .text('Terms & Conditions:', 50, yPos);
            doc.text(quotation.termsConditions, 50, yPos + 15, { width: 500 });
          }

          // Footer
          const pageHeight = doc.page.height;
          doc.fontSize(8)
             .fillColor('#666666')
             .text('Thank you for your business!', 50, pageHeight - 50)
             .text(`Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 50, pageHeight - 35);

          doc.end();

          // Wait for PDF generation to complete
          await new Promise<void>((resolve) => {
            doc.on('end', resolve);
          });

          const pdfBuffer = Buffer.concat(chunks);
          
          pdfAttachment = {
            filename: `quotation-${quotation.quotationNumber}.pdf`,
            content: pdfBuffer,
            contentType: 'application/pdf',
          };
        } catch (pdfError) {
          console.error('Error generating PDF attachment:', pdfError);
          // Continue without PDF attachment
        }
      }

      // Prepare email content
      const emailSubject = validatedData.subject || `Quotation ${quotation.quotationNumber} - ${quotation.customer.name}`;
      
      const emailBody = validatedData.message || `
Dear ${quotation.customer.name},

Please find attached the quotation ${quotation.quotationNumber} for your review.

Quotation Details:
- Quotation Number: ${quotation.quotationNumber}
- Date: ${new Date(quotation.quotationDate).toLocaleDateString()}
- Valid Until: ${quotation.validUntil ? new Date(quotation.validUntil).toLocaleDateString() : 'N/A'}
- Total Amount: ₹${quotation.totalAmount.toLocaleString()}

If you have any questions or need clarification, please don't hesitate to contact us.

Best regards,
${quotation.executive.name}
${quotation.executive.designation || 'Sales Executive'}
KoolSoft Technologies
${quotation.executive.email || ''}
${quotation.executive.phone || ''}
      `.trim();

      // Send email
      const emailService = getEmailService();
      const emailResult = await emailService.sendEmail({
        to: validatedData.to,
        cc: validatedData.cc,
        bcc: validatedData.bcc,
        subject: emailSubject,
        html: emailBody.replace(/\n/g, '<br>'),
        text: emailBody,
        attachments: pdfAttachment ? [pdfAttachment] : undefined,
      });

      if (!emailResult) {
        throw new Error('Failed to send email');
      }

      // Update quotation status to SENT if it was DRAFT
      if (quotation.status === 'DRAFT') {
        await quotationRepository.update(id, {
          status: 'SENT',
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Quotation sent successfully via email',
        data: {
          emailId: emailResult.messageId || 'unknown',
          sentTo: validatedData.to,
          attachmentIncluded: !!pdfAttachment,
        },
      });
    } catch (error) {
      console.error('Error sending quotation email:', error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid email data',
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: 'Failed to send quotation email',
        },
        { status: 500 }
      );
    }
  }
);
