{"/api/models/route": "app/api/models/route.js", "/api/quotations/route": "app/api/quotations/route.js", "/api/products/route": "app/api/products/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/customers/route": "app/api/customers/route.js", "/api/users/route": "app/api/users/route.js", "/quotations/new/page": "app/quotations/new/page.js", "/quotations/page": "app/quotations/page.js"}